<template>
  <transition name="chat-window">
    <div class="ai-chat-window" v-show="isOpen">
      <div class="chat-container">
        <!-- 左侧数字人区域 -->
        <div class="digital-human">
          <img
            src="https://prod.shukeyun.com/maintenance/deepfile/data/2025-05-29/upload_c3aaec13ce053dec3ab8929ce9778c68.png"
            alt="数字人" class="digital-human-image">
        </div>

        <!-- 右侧聊天区域 -->
        <div class="chat-section">
          <div class="messages-container" ref="messagesContainer">
            <message-item v-for="(message, index) in messages" :key="index" :message="message" :data-index="index" />
          </div>
          <div class="input-container">
            <div class="input-box">
              <input type="text" v-model="inputText" @keyup.enter="sendMessage()" placeholder="请输入您的问题，回车即可发送"
                class="chat-input" :disabled="isLoading" ref="chatInput" />
              <button class="send-button" @click="sendMessage()" :disabled="isLoading">
                <img
                  src="https://prod.shukeyun.com/maintenance/deepfile/data/2025-05-29/upload_82d416b7ac77c97869778db8ccc69950.png"
                  alt="发送" class="send-icon">
              </button>
            </div>
          </div>
        </div>
        <!-- 关闭按钮 -->
        <button class="close-btn" @click="$emit('close')">
          <span class="close-icon"></span>
        </button>
      </div>
    </div>
  </transition>
</template>

<script>
import MessageItem from './MessageItem.vue';
import { ChatService } from '../services/chatService';
import config, { isLocalDevelopment } from '../config/environment';

export default {
  name: 'ChatWindow',
  components: {
    MessageItem
  },
  props: {
    isOpen: Boolean,
    chatId: {
      type: String,
      default: () => `chat_${Date.now()}`
    },
    docIds: {
      type: Array,
      default: () => []
    },
    config: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      messages: [],
      inputText: '',
      chatService: null,
      isLoading: false,
      abortController: null,
      currentResponseText: '',
      userScrolled: false,
      localDocIds: []
    };
  },
  created() {
    this.chatService = new ChatService();
  },
  watch: {
    messages() {
      this.$nextTick(() => {
        this.scrollToBottom();
      });
    },
    isOpen(val) {
      console.log('isOpen', val);
      if (val) {
        this.$nextTick(() => {
          this.scrollToBottom();
          // 弹窗打开时自动激活输入框
          this.$refs.chatInput.focus();
          
          // 弹窗打开时获取 docIds
          if (this.config.businessId) {
            console.log('config.businessId', this.config.businessId);
            this.fetchDocIds(this.config.businessId);
          }
        });
      }
    },
    'config.businessId': {
      handler(newVal) {
        if (newVal && this.isOpen) {
          this.fetchDocIds(newVal);
        }
      },
      immediate: true
    }
  },
  mounted() {
    
    // 显示欢迎消息
    this.addMessage({
      text: '您好，我是 AI 客服，有什么可以帮助您？',
      isUser: false
    });

    // 添加键盘事件监听
    document.addEventListener('keydown', this.handleKeyDown);
    
    // 监听滚动事件
    if (this.$refs.messagesContainer) {
      this.$refs.messagesContainer.addEventListener('scroll', this.handleScroll);
    }
    
    // 初始化时如果有 businessId 则获取 docIds
    if (this.config.businessId) {
      this.fetchDocIds(this.config.businessId);
    }
  },
  beforeUnmount() {
    // 移除键盘事件监听
    document.removeEventListener('keydown', this.handleKeyDown);
    // 移除滚动事件监听
    if (this.$refs.messagesContainer) {
      this.$refs.messagesContainer.removeEventListener('scroll', this.handleScroll);
    }
    // 确保中止任何进行中的请求
    if (this.abortController) {
      this.abortController.abort();
    }
  },
  methods: {
    async fetchDocIds(businessId) {
      console.log('fetchDocIds?????????', businessId);
      try {
        let baseApiHost = config.baseApiHost;
        if (isLocalDevelopment()) {
          baseApiHost = '/baseApi';
        }
        const response = await fetch(`${baseApiHost}/common/common-gateway/help-backend/knowledge/ids?businessId=${businessId}`);
        
        if (!response.ok) {
          throw new Error(`获取 docIds 失败：${response.status}`);
        }
        
        const data = await response.json();
        if (data && Array.isArray(data.data)) {
          this.localDocIds = data.data;
          console.log('获取到的 docIds:', this.localDocIds);
        } else {
          console.error('获取 docIds 返回格式不正确：', data);
        }
      } catch (error) {
        console.error('获取 docIds 出错：', error);
      }
    },
    handleKeyDown(event) {
      // 如果按下 ESC 键且弹窗是打开的，则关闭弹窗
      if (event.key === 'Escape' && this.isOpen) {
        this.$emit('close');
      }
    },
    handleScroll() {
      const container = this.$refs.messagesContainer;
      if (!container) return;
      
      // 检测用户是否手动滚动
      const isAtBottom = container.scrollHeight - container.scrollTop <= container.clientHeight + 50;
      this.userScrolled = !isAtBottom;
    },
    addMessage(message) {
      this.messages.push({
        text: message.text,
        isUser: message.isUser,
        isLoading: message.isLoading
      });
    },
    updateLastAIMessage(text) {
      if (this.messages.length > 0) {
        const lastNonUserMessageIndex = [...this.messages].reverse().findIndex(msg => !msg.isUser);
        if (lastNonUserMessageIndex !== -1) {
          const actualIndex = this.messages.length - 1 - lastNonUserMessageIndex;
          this.messages[actualIndex].text = text;
          this.messages[actualIndex].isLoading = false;
          
          // 当 AI 回复完成时，将整个回复内容输出到控制台
          if (text) {
            // console.log('AI 回复内容：', text);
          }
        }
      }
    },
    scrollToBottom() {
      const container = this.$refs.messagesContainer;
      if (container && !this.userScrolled) {
        container.scrollTop = container.scrollHeight;
      }
    },
    async sendMessage(text) {
      if (!this.inputText && !text) return;
      
      const messageText = text || this.inputText;
      this.inputText = '';
      
      // 添加用户消息
      this.addMessage({
        text: messageText,
        isUser: true
      });
      
      // 用户发送消息时重置状态并滚动到底部
      this.userScrolled = false;
      this.$nextTick(() => {
        this.scrollToBottom();
      });
      
      this.isLoading = true;

      // 如果有进行中的请求，先中止
      if (this.abortController) {
        this.abortController.abort();
      }
      
      // 创建新的 AbortController
      this.abortController = new AbortController();
      this.currentResponseText = '';
      
      try {
        // 添加一个空的 AI 回复占位
        this.addMessage({
          text: '',
          isUser: false,
          isLoading: true
        });
        
        // 使用本地获取的 docIds 或者传入的 docIds
        const docIdsToUse = this.localDocIds.length > 0 ? this.localDocIds : this.docIds;
        
        await this.chatService.sendMessageStream(
          {
            question: messageText,
            chatid: this.chatId,
            docids: docIdsToUse
          },
          {
            onMessage: (data) => {
              
              if (typeof data === 'string') {
                this.currentResponseText += data;
              } else if (data && data.text) {
                this.currentResponseText += data.text;
              }
              this.updateLastAIMessage(this.currentResponseText);
              // 如果用户未手动滚动，才滚动到底部
              if (!this.userScrolled) {
                this.$nextTick(() => {
                  this.scrollToBottom();
                });
              }
            },
            onError: (error) => {
              console.error('流式消息处理错误：', error);
              if (!this.currentResponseText) {
                this.updateLastAIMessage('抱歉，发生了错误，请稍后再试。');
              }
            },
            onClose: () => {
              this.isLoading = false;
              if (!this.currentResponseText) {
                this.updateLastAIMessage('抱歉，没有收到回复，请稍后再试。');
              }
              this.$nextTick(() => {
                this.$refs.chatInput.focus();
              });
            }
          },
          this.abortController
        );
      } catch (error) {
        console.error('发送流式消息失败：', error);
        if (!this.currentResponseText) {
          this.updateLastAIMessage('抱歉，发生了错误，请稍后再试。');
        }
      } finally {
        this.isLoading = false;
        this.$nextTick(() => {
          this.$refs.chatInput.focus();
        });
      }
    }
  }
}
</script>

<style scoped lang="less">
.ai-chat-window {
  padding: 5px;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  // width: 800px;
  height: 540px;
  border-radius: 20px;
  background-color: #73b2ff;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
  z-index: 9999;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.chat-container {
  display: flex;
  height: 100%;
  background-image: url('@/assets/image/bg.webp');
  border-radius: 20px;
  background-size: cover;
  background-position: center;
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    // background-color: rgba(255, 255, 255, 0.9);
    z-index: 1;
  }
}

.digital-human {
  width: 420px;
  background-color: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 2;
  overflow: hidden;
}

.digital-human-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.chat-section {
  width: 540px;
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: transparent;
  position: relative;
  z-index: 2;
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 15px;
  padding-right: 80px;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  scroll-behavior: smooth; /* 添加平滑滚动效果 */
  display: flex;
  flex-direction: column;
  
  &::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
  }
}

.input-container {
  padding: 15px;
  background: transparent;
}

.input-box {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.78);
  color: #B0B0B0;
  border-radius: 8px;
  padding: 0 12px;
  height: 44px;
  border: 1px solid #fff;
}

.chat-input {
  flex: 1;
  border: none;
  outline: none;
  background: transparent;
  font-size: 15px;
  color: #333;
  height: 40px;
  padding: 0;
}

.send-button {
  margin-left: 8px;
  background: none;
  border: none;
  color: #1890ff;
  font-size: 16px;
  cursor: pointer;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: center;

  &:hover {
    transform: scale(1.1);
  }

  &:active {
    transform: scale(0.9) rotate(15deg);
  }
}

.send-button:disabled {
  // cursor: not-allowed;
  opacity: 0.5;
}

.send-icon {
  width: 24px;
  height: 24px;
  object-fit: contain;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.send-button:hover .send-icon {
  transform: scale(1.1);
}

.send-button:active .send-icon {
  transform: scale(0.9) rotate(15deg);
}

.loading-indicator {
  display: flex;
  justify-content: center;
  // padding: 10px;
}

.loading-dots {
  display: flex;
  gap: 4px;
  
  span {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #409eff;
    animation: loading 1.4s infinite ease-in-out both;
    
    &:nth-child(1) {
      animation-delay: -0.32s;
    }
    
    &:nth-child(2) {
      animation-delay: -0.16s;
    }
  }
}

@keyframes loading {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

.close-btn {
  position: absolute;
  width: 40px;
  height: 40px;
  border-radius: 10px;
  top: 10px;
  right: 10px;
  background: none;
  border: none;
  font-size: 20px;
  color: white;
  cursor: pointer;
  z-index: 2;
  background-color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;

  &:hover {
    background-color: #f5f5f5;
  }
}

.close-icon {
  position: relative;
  width: 20px;
  height: 20px;

  &::before,
  &::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 2px;
    background-color: #5a5a68;
    top: 50%;
    left: 0;
    border-radius: 3px;
  }

  &::before {
    transform: rotate(45deg);
  }

  &::after {
    transform: rotate(-45deg);
  }
}

/* 添加过渡动画 */
.chat-window-enter-active,
.chat-window-leave-active {
  transition: all 0.3s ease;
}

.chat-window-enter-from,
.chat-window-leave-to {
  opacity: 0;
  transform: translate(-50%, -50%) scale(0.8);
}

.chat-window-enter-to,
.chat-window-leave-from {
  opacity: 1;
  transform: translate(-50%, -50%) scale(1);
}
</style> 