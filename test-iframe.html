<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI SDK 测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .test-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            justify-content: center;
            margin: 20px 0;
        }
        button {
            padding: 10px 20px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        button:hover {
            background: #0056b3;
        }
        .config-display {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            overflow-x: auto;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>AI SDK 功能测试</h1>
        
        <div class="test-section">
            <h3>测试 1: 基本功能测试</h3>
            <p>测试基本的打开/关闭功能，使用百度作为 iframe 内容</p>
            <div class="config-display">
                配置: { iframeUrl: "https://www.baidu.com", position: "bottom-right" }
            </div>
            <div class="test-buttons">
                <button onclick="test1_init()">初始化 SDK</button>
                <button onclick="test1_open()">打开窗口</button>
                <button onclick="test1_close()">关闭窗口</button>
                <button onclick="test1_destroy()">销毁 SDK</button>
            </div>
        </div>

        <div class="test-section">
            <h3>测试 2: 不同位置测试</h3>
            <p>测试不同位置的按钮显示</p>
            <div class="test-buttons">
                <button onclick="test2_bottomRight()">右下角</button>
                <button onclick="test2_bottomLeft()">左下角</button>
                <button onclick="test2_topRight()">右上角</button>
                <button onclick="test2_topLeft()">左上角</button>
            </div>
        </div>

        <div class="test-section">
            <h3>测试 3: 无 URL 配置测试</h3>
            <p>测试未配置 iframeUrl 时的默认提示</p>
            <div class="test-buttons">
                <button onclick="test3_noUrl()">测试无 URL</button>
            </div>
        </div>

        <div class="status" id="status">
            状态: 等待测试
        </div>
    </div>

    <!-- SDK 配置 -->
    <script>
        let currentInstance = null;

        function updateStatus(message) {
            document.getElementById('status').textContent = '状态: ' + message;
        }

        // 测试 1: 基本功能
        function test1_init() {
            if (currentInstance) {
                currentInstance.destroy();
            }
            
            window.AIChatConfig = {
                position: "bottom-right",
                offsetX: 20,
                offsetY: 20,
                iframeUrl: "https://www.baidu.com",
                onOpen: function() {
                    updateStatus("窗口已打开 - 百度页面");
                },
                onClose: function() {
                    updateStatus("窗口已关闭");
                }
            };
            
            if (window.AIChatSDK) {
                currentInstance = window.AIChatSDK.init(window.AIChatConfig);
                updateStatus("SDK 已初始化 - 百度页面");
            } else {
                updateStatus("错误: SDK 未加载");
            }
        }

        function test1_open() {
            if (currentInstance) {
                currentInstance.openChat();
            } else {
                updateStatus("请先初始化 SDK");
            }
        }

        function test1_close() {
            if (currentInstance) {
                currentInstance.closeChat();
            } else {
                updateStatus("请先初始化 SDK");
            }
        }

        function test1_destroy() {
            if (currentInstance) {
                currentInstance.destroy();
                currentInstance = null;
                updateStatus("SDK 已销毁");
            }
        }

        // 测试 2: 不同位置
        function test2_bottomRight() {
            testPosition("bottom-right", "右下角");
        }

        function test2_bottomLeft() {
            testPosition("bottom-left", "左下角");
        }

        function test2_topRight() {
            testPosition("top-right", "右上角");
        }

        function test2_topLeft() {
            testPosition("top-left", "左上角");
        }

        function testPosition(position, name) {
            if (currentInstance) {
                currentInstance.destroy();
            }
            
            window.AIChatConfig = {
                position: position,
                offsetX: 20,
                offsetY: 20,
                iframeUrl: "https://www.bing.com",
                onOpen: function() {
                    updateStatus(`${name}位置窗口已打开`);
                },
                onClose: function() {
                    updateStatus(`${name}位置窗口已关闭`);
                }
            };
            
            if (window.AIChatSDK) {
                currentInstance = window.AIChatSDK.init(window.AIChatConfig);
                updateStatus(`SDK 已初始化 - ${name}位置`);
            }
        }

        // 测试 3: 无 URL
        function test3_noUrl() {
            if (currentInstance) {
                currentInstance.destroy();
            }
            
            window.AIChatConfig = {
                position: "bottom-right",
                offsetX: 20,
                offsetY: 20,
                // 不设置 iframeUrl
                onOpen: function() {
                    updateStatus("无 URL 窗口已打开 - 应显示提示信息");
                },
                onClose: function() {
                    updateStatus("无 URL 窗口已关闭");
                }
            };
            
            if (window.AIChatSDK) {
                currentInstance = window.AIChatSDK.init(window.AIChatConfig);
                updateStatus("SDK 已初始化 - 无 URL 配置");
                // 自动打开窗口以查看提示
                setTimeout(() => {
                    currentInstance.openChat();
                }, 500);
            }
        }
    </script>

    <!-- 加载 SDK -->
    <script src="dist/ai-chat-sdk.js" onload="updateStatus('SDK 已加载，可以开始测试')" onerror="updateStatus('错误：SDK 加载失败')"></script>
</body>
</html>
