<template>
  <div 
    class="ai-chat-button" 
    :style="buttonStyle"
    @click="$emit('click')"
  >
    <img src="@/assets/image/service-icon.png" mode="scaleToFill" />
    <div class="zx-btn">在线咨询</div>
  </div>
</template>

<script>
export default {
  name: 'ChatButton',
  props: {
    position: {
      type: String,
      default: 'bottom-right',
      validator: (value) => ['bottom-right', 'bottom-left', 'top-right', 'top-left'].includes(value)
    },
    offsetX: {
      type: Number,
      default: 20
    },
    offsetY: {
      type: Number,
      default: 20
    }
  },
  computed: {
    buttonStyle() {
      const style = {
        position: 'fixed'
      };
      
      // 根据位置设置定位
      if (this.position.includes('bottom')) {
        style.bottom = `${this.offsetY}px`;
      } else {
        style.top = `${this.offsetY}px`;
      }
      
      if (this.position.includes('right')) {
        style.right = `${this.offsetX}px`;
      } else {
        style.left = `${this.offsetX}px`;
      }
      
      return style;
    }
  }
}
</script>

<style scoped>
.ai-chat-button {
  position: fixed;
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  z-index: 100;
}

.ai-chat-button img {
  width: 112px;
  height: 112px;
}

.zx-btn {
  margin-top: -22px;
  font-size: 14px;
  color: #333;
  background: linear-gradient(180deg, #4BC6FB 0%, #1890FF 100%);
    border-radius: 2px;
    width: 72px;
      height: 24px;
      line-height: 24px;
      text-align: center;
      color: #fff;
}
</style> 