<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>AI客服SDK使用指南</title>
  <style>
    :root {
      --primary-color: #4a86e8;
      --primary-hover: #3b76d8;
      --bg-color: #f8f9fa;
      --card-bg: #ffffff;
      --text-color: #333333;
      --border-color: #e1e4e8;
      --code-bg: #f5f7f9;
    }
    
    body {
      font-family: 'PingFang SC', 'Microsoft YaHei', Arial, sans-serif;
      margin: 0;
      padding: 20px;
      background-color: var(--bg-color);
      color: var(--text-color);
      transition: all 0.3s ease;
      line-height: 1.6;
    }
    
    .container {
      max-width: 900px;
      margin: 0 auto;
      background-color: var(--card-bg);
      padding: 30px;
      border-radius: 12px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    }
    
    h1 {
      color: var(--primary-color);
      text-align: center;
      margin-bottom: 30px;
      font-weight: 600;
    }

    h2 {
      color: var(--primary-color);
      margin-top: 30px;
      margin-bottom: 15px;
      font-weight: 500;
      border-bottom: 1px solid var(--border-color);
      padding-bottom: 8px;
    }
    
    .section {
      margin-bottom: 30px;
    }

    .demo-buttons {
      display: flex;
      gap: 15px;
      flex-wrap: wrap;
      margin-top: 25px;
      justify-content: center;
    }
    
    button {
      padding: 10px 15px;
      background-color: var(--primary-color);
      color: white;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-weight: 500;
      transition: all 0.2s ease;
      min-width: 120px;
    }
    
    button:hover {
      background-color: var(--primary-hover);
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
    
    button:active {
      transform: translateY(0);
    }
    
    code {
      background-color: var(--code-bg);
      padding: 2px 6px;
      border-radius: 4px;
      font-family: Consolas, Monaco, 'Andale Mono', monospace;
      font-size: 0.9em;
    }
    
    pre {
      background-color: var(--code-bg);
      padding: 15px;
      border-radius: 8px;
      overflow-x: auto;
      margin: 15px 0;
    }
    
    pre code {
      background: none;
      padding: 0;
    }
    
    .status {
      background-color: #f1f8ff;
      border: 1px solid #c8e1ff;
      padding: 12px;
      border-radius: 6px;
      margin-top: 20px;
      font-size: 14px;
      text-align: center;
    }
    
    /* 暗黑模式 */
    body.dark-mode {
      --bg-color: #1a1a1a;
      --card-bg: #2d2d2d;
      --text-color: #eaeaea;
      --border-color: #444444;
      --code-bg: #333;
    }
    
    .toggle-dark-mode {
      position: fixed;
      top: 20px;
      right: 20px;
      background-color: var(--card-bg);
      border: 1px solid var(--border-color);
      border-radius: 50%;
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      z-index: 999;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    
    /* 响应式调整 */
    @media (max-width: 768px) {
      .container {
        padding: 20px 15px;
      }
    }
  </style>
</head>
<body>
  <div class="toggle-dark-mode" onclick="toggleDarkMode()">
    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
      <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"></path>
    </svg>
  </div>
  
  <div class="container">
    <h1>AI SDK 使用指南</h1>

    <div class="section">
      <h2>SDK 简介</h2>
      <p>AI SDK 是一个轻量级的弹窗组件，可以轻松集成到任何网站中，提供灵活的 iframe 内容展示。主要特点：</p>
      <ul>
        <li>只需引入单个 JS 文件</li>
        <li>支持暗黑模式和自定义主题</li>
        <li>可自定义位置和外观</li>
        <li>简单的 API 接口</li>
        <li>灵活的 iframe 内容加载</li>
      </ul>
    </div>

    <div class="section">
      <h2>安装方法</h2>
      <p>将 SDK 脚本添加到您的 HTML 页面：</p>
      <pre><code>&lt;script src="https://你的域名/ai-chat-sdk.js"&gt;&lt;/script&gt;</code></pre>
    </div>

    <div class="section">
      <h2>基本用法</h2>
      <p>添加配置对象并初始化 SDK：</p>
      <pre><code>// 配置SDK
window.AIChatConfig = {
  position: "bottom-right", // 'bottom-right', 'bottom-left', 'top-right', 'top-left'
  offsetX: 20,              // 水平偏移（像素）
  offsetY: 20,              // 垂直偏移（像素）
  iframeUrl: "https://example.com", // iframe 要加载的 URL
  onOpen: function() {
    console.log("窗口已打开");
  },
  onClose: function() {
    console.log("窗口已关闭");
  }
};

// SDK会自动初始化
// 如果需要手动初始化，可以使用：
// window.aiChatInstance = window.AIChatSDK.init(window.AIChatConfig);</code></pre>
    </div>

    <div class="section">
      <h2>API 方法</h2>
      <ul>
        <li><code>AIChatSDK.init(config)</code> - 初始化 SDK 并返回实例</li>
        <li><code>aiChatInstance.openChat()</code> - 打开窗口</li>
        <li><code>aiChatInstance.closeChat()</code> - 关闭窗口</li>
        <li><code>aiChatInstance.destroy()</code> - 销毁 SDK 实例</li>
      </ul>
    </div>

    <div class="section">
      <h2>实时演示</h2>
      <p>本页面已集成 AI SDK，您可以通过下面的按钮进行交互：</p>

      <div class="demo-buttons">
        <button onclick="openChat()">打开窗口</button>
        <button onclick="closeChat()">关闭窗口</button>
        <button onclick="reloadSDK()">重新加载 SDK</button>
      </div>
      
      <div class="status" id="status">
        状态：SDK 已初始化
      </div>
    </div>
  </div>

  <!-- SDK 配置 -->
  <script>
    // 初始 SDK 配置
    window.AIChatConfig = {
      position: "bottom-right",
      offsetX: 20,
      offsetY: 20,
      iframeUrl: "https://www.baidu.com", // 示例 URL，可以替换为任何网页
      onOpen: function() {
        updateStatus("窗口已打开");
      },
      onClose: function() {
        updateStatus("窗口已关闭");
      }
    };
    
    // 更新状态消息
    function updateStatus(message) {
      const statusElement = document.getElementById('status');
      const timestamp = new Date().toLocaleTimeString();
      statusElement.innerHTML = `状态：${message} <span style="color:#888;font-size:12px;">(${timestamp})</span>`;
    }
    
    // 切换网页暗黑模式（不影响 SDK）
    function toggleDarkMode() {
      document.body.classList.toggle('dark-mode');
    }
    
    // 重新加载 SDK
    function reloadSDK() {
      if (window.aiChatInstance) {
        window.aiChatInstance.destroy();
      }
      window.aiChatInstance = window.AIChatSDK.init(window.AIChatConfig);
      updateStatus("SDK 已重新加载");
    }

    // 打开聊天
    function openChat() {
      if (window.aiChatInstance) {
        window.aiChatInstance.openChat();
      } else {
        updateStatus("错误：SDK 实例未初始化");
        // 尝试初始化 SDK
        if (window.AIChatSDK) {
          window.aiChatInstance = window.AIChatSDK.init(window.AIChatConfig);
          updateStatus("SDK 已自动初始化");
          setTimeout(() => window.aiChatInstance.openChat(), 100);
        } else {
          updateStatus("错误：无法找到 AIChatSDK，请检查脚本是否正确加载");
        }
      }
    }

    // 关闭聊天
    function closeChat() {
      if (window.aiChatInstance) {
        window.aiChatInstance.closeChat();
      } else {
        updateStatus("错误：SDK 实例未初始化");
      }
    }



    // 页面加载后自动初始化 SDK
    document.addEventListener('DOMContentLoaded', function() {
      try {
        if (window.AIChatSDK) {
          window.aiChatInstance = window.AIChatSDK.init(window.AIChatConfig);
          updateStatus("SDK 已成功初始化");
        } else {
          updateStatus("等待 SDK 加载...");
          // 如果 SDK 脚本加载较慢，等待一段时间后再次尝试初始化
          setTimeout(function() {
            if (window.AIChatSDK) {
              window.aiChatInstance = window.AIChatSDK.init(window.AIChatConfig);
              updateStatus("SDK 已成功初始化");
            } else {
              updateStatus("错误：无法加载 SDK，请检查网络或脚本路径");
            }
          }, 1000);
        }
      } catch (error) {
        updateStatus("SDK 初始化错误：" + error.message);
      }
    });
  </script>

  <!-- 加载 SDK -->
  <script src="../dist/ai-chat-sdk.js" onerror="updateStatus('错误：SDK脚本加载失败，请检查路径')"></script>
</body>
</html> 